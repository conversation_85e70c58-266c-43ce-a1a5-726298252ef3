"use client";
import { useState, useEffect } from "react";

const propertyData = [
  {
    id: 1,
    title: "Luxury Boys PG - Koramangala",
    type: "PG",
    price: "₹15,000",
    period: "/month",
    location: "Koramangala, Bangalore",
    features: [
      "🛏️ Single Room",
      "🍽️ Food Included",
      "📶 WiFi",
      "🧺 Laundry",
      "❄️ AC",
      "🚿 Attached Bath",
    ],
    amenities: [
      "🏋️ Gym",
      "🅿️ Parking",
      "🔒 Security",
      "🧹 Housekeeping",
      "📺 Common TV",
      "🔥 Hot Water",
    ],
    image: "🏨",
    rating: 4.8,
    reviews: 156,
    description:
      "Premium boys PG with all modern amenities in the heart of Koramangala. Safe, secure, and comfortable living.",
    contact: { name: "<PERSON><PERSON>", phone: "+91 98765 43210" },
  },
  {
    id: 2,
    title: "Girls PG - HSR Layout",
    type: "PG",
    price: "₹12,000",
    period: "/month",
    location: "HSR Layout, Bangalore",
    features: [
      "🛏️ Double Sharing",
      "🍽️ Home Food",
      "📶 High Speed WiFi",
      "🧺 Laundry",
      "❄️ AC",
      "🚿 Attached Bath",
    ],
    amenities: [
      "🔒 24/7 Security",
      "🧹 Daily Cleaning",
      "📺 Recreation Room",
      "🔥 Hot Water",
      "🅿️ Two Wheeler Parking",
    ],
    image: "🏩",
    rating: 4.9,
    reviews: 203,
    description:
      "Safe and secure girls PG with homely environment and nutritious meals. Perfect for working women.",
    contact: { name: "Priya Sharma", phone: "+91 87654 32109" },
  },
  {
    id: 3,
    title: "2BHK Luxury Flat - Whitefield",
    type: "Flat",
    price: "₹35,000",
    period: "/month",
    location: "Whitefield, Bangalore",
    features: [
      "🏠 2 BHK",
      "🛋️ Fully Furnished",
      "❄️ AC in all rooms",
      "🍽️ Modular Kitchen",
      "🚿 2 Bathrooms",
    ],
    amenities: [
      "🏋️ Gym",
      "🏊 Swimming Pool",
      "🅿️ Covered Parking",
      "🔒 Gated Community",
      "🌳 Garden",
      "🛡️ 24/7 Security",
    ],
    image: "🏢",
    rating: 4.7,
    reviews: 89,
    description:
      "Spacious 2BHK flat in premium gated community with world-class amenities and excellent connectivity.",
    contact: { name: "Amit Singh", phone: "+91 76543 21098" },
  },
  {
    id: 4,
    title: "Studio Apartment - Indiranagar",
    type: "Flat",
    price: "₹18,000",
    period: "/month",
    location: "Indiranagar, Bangalore",
    features: [
      "🏠 Studio",
      "🛋️ Semi-Furnished",
      "❄️ AC",
      "🍽️ Kitchen",
      "🚿 Attached Bath",
      "🌅 Balcony",
    ],
    amenities: [
      "🅿️ Parking",
      "🔒 Security",
      "🛗 Elevator",
      "💧 Water Supply",
      "⚡ Power Backup",
    ],
    image: "🏠",
    rating: 4.5,
    reviews: 67,
    description:
      "Cozy studio apartment perfect for young professionals. Great location with easy access to metro.",
    contact: { name: "Sneha Patel", phone: "+91 65432 10987" },
  },
  {
    id: 5,
    title: "Premium Co-living Space",
    type: "PG",
    price: "₹20,000",
    period: "/month",
    location: "Electronic City, Bangalore",
    features: [
      "🛏️ Private Room",
      "🍽️ Gourmet Meals",
      "📶 Fiber Internet",
      "🧺 Premium Laundry",
      "❄️ AC",
      "🚿 Designer Bath",
    ],
    amenities: [
      "🏋️ Fitness Center",
      "🎮 Gaming Zone",
      "📚 Library",
      "☕ Cafe",
      "🎬 Movie Room",
      "🧘 Yoga Studio",
    ],
    image: "🏨",
    rating: 4.9,
    reviews: 234,
    description:
      "Ultra-modern co-living space with premium amenities and vibrant community. Perfect for tech professionals.",
    contact: { name: "Vikram Reddy", phone: "+91 98765 12345" },
  },
  {
    id: 6,
    title: "3BHK Family Flat - Jayanagar",
    type: "Flat",
    price: "₹45,000",
    period: "/month",
    location: "Jayanagar, Bangalore",
    features: [
      "🏠 3 BHK",
      "🛋️ Fully Furnished",
      "❄️ AC in all rooms",
      "🍽️ Modular Kitchen",
      "🚿 3 Bathrooms",
      "🌅 2 Balconies",
    ],
    amenities: [
      "🏫 Kids Play Area",
      "🅿️ Car Parking",
      "🔒 Gated Society",
      "🌳 Garden",
      "🛡️ Security",
      "🏪 Shopping Complex",
    ],
    image: "🏘️",
    rating: 4.8,
    reviews: 145,
    description:
      "Spacious family flat in prime location with excellent schools and hospitals nearby. Perfect for families.",
    contact: { name: "Meera Krishnan", phone: "+91 87654 98765" },
  },
  {
    id: 7,
    title: "Student PG - Zero KM from Bhaskaracharya College",
    type: "Flat",
    price: "Contact for Price",
    period: "",
    location: "Zero Kilometer Proximity from Bhaskaracharya College",
    features: [
      "🛏️ Sharing Rooms Available",
      "🏠 Personal Room Basis",
      "🎓 Student Focused",
      "📚 Study Environment",
      "🚶 Walking Distance to College",
      "🏫 Near Bhaskaracharya College",
    ],
    amenities: [
      "🌟 All Amenities Loaded",
      "🔒 Secure Environment",
      "🧹 Housekeeping",
      "📶 WiFi",
      "🍽️ Mess Facility",
      "🅿️ Parking",
    ],
    image: "🏨",
    rating: 4.5,
    reviews: 25,
    description:
      "PG for students located at zero kilometer proximity from Bhaskaracharya College. Loaded with all amenities on sharing and personal room basis.",
    contact: { name: "Property Manager", phone: "+91 98735 60600" },
  },
];

export default function RentalsPage() {
  const [filteredProperties, setFilteredProperties] = useState(propertyData);
  const [filterType, setFilterType] = useState("All");
  const [sortBy, setSortBy] = useState("rating");
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  useEffect(() => {
    let filtered = propertyData;

    if (filterType !== "All") {
      filtered = propertyData.filter(
        (property) => property.type === filterType
      );
    }

    filtered.sort((a, b) => {
      if (sortBy === "price") {
        return (
          parseInt(a.price.replace(/[₹,]/g, "")) -
          parseInt(b.price.replace(/[₹,]/g, ""))
        );
      } else if (sortBy === "rating") {
        return b.rating - a.rating;
      }
      return 0;
    });

    setFilteredProperties(filtered);
  }, [filterType, sortBy]);

  const handleContact = (contact) => {
    const message = `Hi ${contact.name}, I'm interested in the property. Can you please provide more details?`;
    const whatsappUrl = `https://wa.me/${contact.phone.replace(
      /[^0-9]/g,
      ""
    )}?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, "_blank");
  };

  return (
    <div className="rentals-page">
      {/* Hero Section */}
      <section className="hero-section">
        <div className="hero-content">
          <h1 className={`hero-title ${isVisible ? "animate-fadeInUp" : ""}`}>
            🏠 Find Your Perfect Home
          </h1>
          <p className={`hero-subtitle ${isVisible ? "animate-fadeInUp" : ""}`}>
            Discover amazing PGs and flats with premium amenities and modern
            features
          </p>
        </div>
      </section>

      {/* Filters Section */}
      <section className="filters-section">
        <div className="container">
          <div className="filters-container">
            <div className="filter-group">
              <label>🏷️ Property Type:</label>
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="filter-select"
              >
                <option value="All">All Properties</option>
                <option value="PG">🏨 PG</option>
                <option value="Flat">🏢 Flat</option>
              </select>
            </div>
            <div className="filter-group">
              <label>📊 Sort By:</label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="filter-select"
              >
                <option value="rating">⭐ Rating</option>
                <option value="price">💰 Price</option>
              </select>
            </div>
          </div>
        </div>
      </section>

      {/* Properties Grid */}
      <section className="properties-section">
        <div className="container">
          <div className="properties-grid">
            {filteredProperties.map((property, index) => (
              <div
                key={property.id}
                className="property-card"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="property-header">
                  <div className="property-image">
                    <span className="property-icon">{property.image}</span>
                    <div className="property-type-badge">{property.type}</div>
                  </div>
                  <div className="property-rating">
                    <span className="rating-stars">⭐</span>
                    <span className="rating-value">{property.rating}</span>
                    <span className="rating-reviews">({property.reviews})</span>
                  </div>
                </div>

                <div className="property-content">
                  <h3 className="property-title">{property.title}</h3>
                  <div className="property-location">
                    <span className="location-icon">📍</span>
                    {property.location}
                  </div>

                  <div className="property-price">
                    <span className="price-amount">{property.price}</span>
                    <span className="price-period">{property.period}</span>
                  </div>

                  <p className="property-description">{property.description}</p>

                  <div className="property-features">
                    <h4>🏠 Features:</h4>
                    <div className="features-grid">
                      {property.features.map((feature, idx) => (
                        <span key={idx} className="feature-tag">
                          {feature}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div className="property-amenities">
                    <h4>✨ Amenities:</h4>
                    <div className="amenities-grid">
                      {property.amenities.map((amenity, idx) => (
                        <span key={idx} className="amenity-tag">
                          {amenity}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div className="property-contact">
                    <div className="contact-info">
                      <span className="contact-name">
                        👤 {property.contact.name}
                      </span>
                      <span className="contact-phone">
                        📱 {property.contact.phone}
                      </span>
                    </div>
                    <button
                      className="contact-btn"
                      onClick={() => handleContact(property.contact)}
                    >
                      💬 WhatsApp
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      <style jsx>{`
        .rentals-page {
          min-height: 100vh;
        }

        .hero-section {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          padding: 4rem 0;
          text-align: center;
          color: white;
          position: relative;
          overflow: hidden;
        }

        .hero-section::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
          animation: float 15s ease-in-out infinite;
        }

        @keyframes float {
          0%,
          100% {
            transform: translateX(0px) translateY(0px);
          }
          50% {
            transform: translateX(-10px) translateY(-10px);
          }
        }

        .hero-content {
          position: relative;
          z-index: 2;
          max-width: 800px;
          margin: 0 auto;
          padding: 0 2rem;
        }

        .hero-title {
          font-size: 3rem;
          font-weight: 800;
          margin-bottom: 1rem;
          text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .hero-subtitle {
          font-size: 1.2rem;
          opacity: 0.95;
          font-weight: 300;
        }

        .filters-section {
          background: rgba(255, 255, 255, 0.95);
          padding: 2rem 0;
          backdrop-filter: blur(10px);
          border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .container {
          max-width: 1200px;
          margin: 0 auto;
          padding: 0 2rem;
        }

        .filters-container {
          display: flex;
          gap: 2rem;
          justify-content: center;
          flex-wrap: wrap;
        }

        .filter-group {
          display: flex;
          align-items: center;
          gap: 0.5rem;
        }

        .filter-group label {
          font-weight: 600;
          color: #1e293b;
        }

        .filter-select {
          padding: 0.7rem 1rem;
          border: 2px solid #e2e8f0;
          border-radius: 10px;
          background: white;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .filter-select:focus {
          outline: none;
          border-color: #667eea;
          box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .properties-section {
          padding: 3rem 0;
          background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
        }

        .properties-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
          gap: 2rem;
        }

        .property-card {
          background: white;
          border-radius: 20px;
          overflow: hidden;
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;
          animation: slideInUp 0.8s ease-out both;
        }

        .property-card:hover {
          transform: translateY(-10px);
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .property-header {
          position: relative;
          height: 200px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .property-image {
          position: relative;
        }

        .property-icon {
          font-size: 4rem;
          animation: bounce 2s infinite;
        }

        .property-type-badge {
          position: absolute;
          top: 1rem;
          left: 1rem;
          background: rgba(255, 255, 255, 0.2);
          color: white;
          padding: 0.5rem 1rem;
          border-radius: 20px;
          font-weight: 600;
          backdrop-filter: blur(10px);
        }

        .property-rating {
          position: absolute;
          top: 1rem;
          right: 1rem;
          background: rgba(255, 255, 255, 0.2);
          color: white;
          padding: 0.5rem 1rem;
          border-radius: 20px;
          backdrop-filter: blur(10px);
          display: flex;
          align-items: center;
          gap: 0.3rem;
        }

        .property-content {
          padding: 2rem;
        }

        .property-title {
          font-size: 1.4rem;
          font-weight: 700;
          margin-bottom: 0.5rem;
          color: #1e293b;
        }

        .property-location {
          color: #64748b;
          margin-bottom: 1rem;
          display: flex;
          align-items: center;
          gap: 0.5rem;
        }

        .property-price {
          font-size: 1.8rem;
          font-weight: 800;
          color: #667eea;
          margin-bottom: 1rem;
        }

        .property-description {
          color: #64748b;
          line-height: 1.6;
          margin-bottom: 1.5rem;
        }

        .property-features h4,
        .property-amenities h4 {
          font-size: 1rem;
          font-weight: 600;
          margin-bottom: 0.8rem;
          color: #1e293b;
        }

        .features-grid,
        .amenities-grid {
          display: flex;
          flex-wrap: wrap;
          gap: 0.5rem;
          margin-bottom: 1.5rem;
        }

        .feature-tag,
        .amenity-tag {
          background: #f1f5f9;
          padding: 0.4rem 0.8rem;
          border-radius: 15px;
          font-size: 0.85rem;
          color: #475569;
          border: 1px solid #e2e8f0;
        }

        .property-contact {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-top: 1rem;
          border-top: 1px solid #e2e8f0;
        }

        .contact-info {
          display: flex;
          flex-direction: column;
          gap: 0.3rem;
        }

        .contact-name,
        .contact-phone {
          font-size: 0.9rem;
          color: #64748b;
        }

        .contact-btn {
          background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
          color: white;
          border: none;
          padding: 0.8rem 1.5rem;
          border-radius: 25px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          gap: 0.5rem;
        }

        .contact-btn:hover {
          transform: scale(1.05);
          box-shadow: 0 5px 15px rgba(37, 211, 102, 0.3);
        }

        @keyframes slideInUp {
          from {
            opacity: 0;
            transform: translateY(50px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes bounce {
          0%,
          20%,
          50%,
          80%,
          100% {
            transform: translateY(0);
          }
          40% {
            transform: translateY(-10px);
          }
          60% {
            transform: translateY(-5px);
          }
        }

        .animate-fadeInUp {
          animation: fadeInUp 1s ease-out;
        }

        @media (max-width: 768px) {
          .hero-title {
            font-size: 2.5rem;
          }

          .properties-grid {
            grid-template-columns: 1fr;
          }

          .filters-container {
            flex-direction: column;
            align-items: center;
          }

          .property-contact {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;
          }

          .contact-btn {
            justify-content: center;
          }
        }
      `}</style>
    </div>
  );
}
