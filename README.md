# 🏠 PG and Flat Details - Find Your Perfect Home

A modern, responsive, and feature-rich web application for finding PGs and flats for rent. Built with Next.js, featuring stunning animations, awesome UI, and comprehensive property details.

## ✨ Features

### 🎨 Modern & Aesthetic UI
- **Stunning Animations**: Smooth transitions, hover effects, and loading animations
- **Responsive Design**: Perfect on all devices - mobile, tablet, and desktop
- **Modern Gradients**: Beautiful color schemes with gradient backgrounds
- **Interactive Elements**: Hover effects, smooth scrolling, and engaging interactions

### 🏠 Property Features
- **Comprehensive Listings**: Detailed PG and flat information
- **Smart Filtering**: Filter by property type (PG/Flat) and sort by rating/price
- **Rich Property Data**: Features, amenities, pricing, location, and contact details
- **Rating System**: Star ratings and customer reviews
- **WhatsApp Integration**: Direct contact via WhatsApp

### 🚀 Technical Features
- **Next.js 15**: Latest version with App Router
- **Server-Side Rendering**: Fast loading and SEO optimized
- **Custom CSS**: Hand-crafted styles with animations
- **Responsive Grid**: Auto-adjusting property cards
- **Smooth Scrolling**: Enhanced user experience

## 🏢 Property Types

### 🏨 PG (Paying Guest)
- Single/Double sharing rooms
- Food included options
- WiFi and laundry services
- AC and attached bathrooms
- Security and housekeeping

### 🏢 Flats
- Studio, 1BHK, 2BHK, 3BHK options
- Furnished/Semi-furnished
- Modern amenities
- Gated communities
- Parking facilities

## 👥 Support Team

Our dedicated support team is available 24/7:

- **Vinay Kumar** - Property Manager 📱 +91 98765 43210
- **Priya Sharma** - Customer Support 📱 +91 87654 32109
- **Amit Singh** - Technical Support 📱 +91 76543 21098
- **Sneha Patel** - Booking Specialist 📱 +91 65432 10987

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd anjanaye
```

2. **Install dependencies**
```bash
npm install
# or
yarn install
```

3. **Run the development server**
```bash
npm run dev
# or
yarn dev
```

4. **Open your browser**
Navigate to [http://localhost:3001](http://localhost:3001) to see the application.

## 📱 Pages & Sections

### 🏠 Home Page (`/`)
- **Hero Section**: Animated slideshow with call-to-action
- **Features Section**: Why choose us with animated cards
- **Stats Section**: Company statistics and achievements
- **About Section**: Company information and team details
- **CTA Section**: Final call-to-action

### 🏢 Properties Page (`/rentals`)
- **Property Listings**: Grid of available PGs and flats
- **Advanced Filtering**: Type and price sorting
- **Detailed Cards**: Comprehensive property information
- **Contact Integration**: Direct WhatsApp contact

## 🎨 Design Features

### 🌈 Color Scheme
- **Primary**: Purple gradient (#667eea to #764ba2)
- **Secondary**: Blue gradient (#60a5fa)
- **Accent**: Orange (#f59e0b) for CTAs
- **Background**: Light gradients for sections

### 📱 Responsive Breakpoints
- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

### ✨ Animations
- **Slide In**: Property cards animate on scroll
- **Bounce**: Icons and emojis with bounce effects
- **Fade In**: Text elements with staggered animations
- **Hover Effects**: Transform and shadow animations

## 🛠️ Tech Stack

- **Framework**: Next.js 15.4.5
- **React**: 19.1.0
- **Styling**: Custom CSS with CSS-in-JS
- **Fonts**: Geist Sans & Geist Mono
- **Icons**: Emoji-based icon system
- **Responsive**: CSS Grid & Flexbox

## 📦 Project Structure

```
anjanaye/
├── app/
│   ├── globals.css          # Global styles
│   ├── layout.js           # Root layout with header/footer
│   ├── page.js             # Home page
│   └── rentals/
│       └── page.js         # Properties listing page
├── public/
│   ├── manifest.json       # PWA manifest
│   └── *.svg              # Icon files
├── package.json
└── README.md
```

## 🌟 Key Components

### Header
- Fixed navigation with logo "PG and Flat Details"
- Smooth animations and hover effects
- Responsive mobile menu

### Property Cards
- Animated property listings
- Rating system with stars
- Feature and amenity tags
- WhatsApp contact integration

### Footer
- Support team contact details
- Company information
- Quick links and social proof

## 📈 Performance Features

- **Fast Loading**: Optimized images and code splitting
- **SEO Friendly**: Meta tags and structured data
- **Smooth Animations**: Hardware-accelerated CSS animations
- **Mobile Optimized**: Touch-friendly interface

## 🔧 Customization

### Adding New Properties
Edit the `propertyData` array in `/app/rentals/page.js`:

```javascript
{
  id: 7,
  title: 'Your Property Title',
  type: 'PG', // or 'Flat'
  price: '₹20,000',
  period: '/month',
  location: 'Your Location',
  features: ['🛏️ Feature 1', '🍽️ Feature 2'],
  amenities: ['🏋️ Amenity 1', '🅿️ Amenity 2'],
  // ... more fields
}
```

### Styling Customization
- Modify CSS variables in `globals.css`
- Update gradient colors in component styles
- Adjust animation timings and effects

## 🚀 Deployment

### Vercel (Recommended)
```bash
npm run build
# Deploy to Vercel
```

### Other Platforms
```bash
npm run build
npm start
```

## 📞 Contact & Support

For any queries or support:
- 📧 Email: <EMAIL>
- 📱 Phone: +91 98765 43210
- 🌐 Website: [Your Website URL]

## 📄 License

This project is created for educational and demonstration purposes.

---

**Made with ❤️ by Vinay Kumar! 🏡**
