"use client";
import { useState, useEffect } from "react";

const propertyData = [
  {
    id: 1,
    title: "🏡 BOYS PG Near BHASKARACHARYA College 🏫",
    type: "PG",
    price: "₹8,000",
    period: "/month (Negotiable)",
    location:
      "Near Bhaskaracharya College, Ekta Market, Gali No 53, Mahavir Enclave Part 3",
    features: [
      "🛏️ NON AC Double Sharing",
      "🍽️ All 3 Meals",
      "💧 RO Water",
      "📹 CCTV Surveillance",
      "🧺 Washing Machine",
      "📶 High-Speed Wi-Fi",
    ],
    amenities: [
      "🧹 Housekeeping & Maintenance",
      "⚡ Electricity Included",
      "🔒 Secure Stay",
      "🏠 Boys Only",
      "💰 Budget Friendly",
      "📞 Contact Available",
    ],
    image: "🏠",
    rating: 4.8,
    reviews: 45,
    description:
      "✨ Comfortable & Secure Stay Options for BOYS. Budget-friendly accommodation with all essential facilities included. Perfect for students near Bhaskaracharya College.",
    contact: { name: "<PERSON><PERSON><PERSON>", phone: "+91 93057 73385" },
    specialNote: "⚡ Electricity charges are included",
  },
  {
    id: 2,
    title: "🏡 Shree Shyam PG - BOYS Near BHASKARACHARYA College 🏫",
    type: "PG",
    price: "₹8,000",
    period: "/month",
    location:
      "Shree Shyam PG, Near Bhaskaracharya College, Ekta Market, Mahavir Enclave Part 3",
    features: [
      "🛏️ Double Sharing Room",
      "🍽️ All 3 Meals (Home made)",
      "💧 RO Water",
      "📹 CCTV Surveillance",
      "🧺 Washing Machine",
      "📶 High-Speed Wi-Fi",
    ],
    amenities: [
      "🧹 Housekeeping & Maintenance",
      "⚡ Electricity Included",
      "🔒 Secure Stay",
      "🏠 Boys Only",
      "🏡 Shree Shyam PG",
      "💰 Budget Friendly",
    ],
    image: "🏠",
    rating: 4.7,
    reviews: 38,
    description:
      "✨ Comfortable & Secure Stay Options for BOYS at Shree Shyam PG. Home-made meals and all essential facilities included. Perfect location near Bhaskaracharya College.",
    contact: { name: "Anjanaye Kumar", phone: "+91 93057 73385" },
    specialNote: "⚡ Electricity charges are included",
  },
  {
    id: 3,
    title: "💖 Aunty's Girls PG - Homely Stay 🏠",
    type: "PG",
    price: "₹8,000",
    period: "/month",
    location: "Prime Location - Girls Only",
    features: [
      "📶 WiFi",
      "🍽️ 3-Time Meals",
      "🧺 Laundry",
      "🛋️ Fully Furnished",
      "🔥 Hot Water (Geyser) in Winters ❄️",
      "🏠 Homely Environment",
    ],
    amenities: [
      "💖 Aunty's Care",
      "🏠 Feel at Home",
      "👩‍👧‍👦 Girls Only",
      "✨ Away from Home",
      "🤗 Homely Atmosphere",
      "💝 Personal Care",
      "Flats are also Available"
    ],
    image: "🏩",
    rating: 4.9,
    reviews: 87,
    description:
      "💖 Looking for a homely PG? Aunty's Girls PG is here! Feel at home, away from home with all essential facilities and a caring environment. Perfect for girls seeking comfort and security. Flats are also Available",
    contact: { name: "Anjanaye Kumar", phone: "+91 93057 73385" },
    specialNote: "🏠✨ Feel at home, away from home!",
  },
  {
    id: 4,
    title: "!! जय गुरू जी !! 💖 Aunty's Girls PG 🏠",
    type: "PG",
    price: "₹8,500",
    period: "/month",
    location: "Prime Location - Girls Only",
    features: [
      "📶 Free Wi-Fi",
      "💧 RO Water",
      "🛋️ Fully Furnished Rooms",
      "🍽️ 3 Time Food Available",
      "🏠 घर जैसा एहसास",
      "💰 सबसे सस्ता और अच्छा",
      "Flats are also Available",
    ],
    amenities: [
      "💖 Aunty's Care",
      "🏠 Home-like Feel",
      "👩‍👧‍👦 Girls Only",
      "✨ Best & Affordable",
      "🤗 Homely Atmosphere",
      "🙏 जय गुरू जी",
    ],
    image: "🏩",
    rating: 4.9,
    reviews: 95,
    description:
      "!! जय गुरू जी !! Aunty's Girls PG - घर जैसा एहसास सबसे सस्ता और अच्छा। Free Wi-Fi, RO Water, Fully Furnished Rooms, और 3 Time Food Available। Perfect homely environment for girls. Flats are also Available",
    contact: { name: "Anjanaye Kumar", phone: "+91 93057 73385" },
    specialNote: "🙏 जय गुरू जी - घर जैसा एहसास!",
  },
];

export default function Home() {
  const [filteredProperties, setFilteredProperties] = useState(propertyData);
  const [filterType, setFilterType] = useState("All");
  const [sortBy, setSortBy] = useState("rating");

  useEffect(() => {
    let filtered = propertyData;

    if (filterType !== "All") {
      filtered = propertyData.filter(
        (property) => property.type === filterType
      );
    }

    filtered.sort((a, b) => {
      if (sortBy === "price") {
        return (
          parseInt(a.price.replace(/[₹,]/g, "")) -
          parseInt(b.price.replace(/[₹,]/g, ""))
        );
      } else if (sortBy === "rating") {
        return b.rating - a.rating;
      }
      return 0;
    });

    setFilteredProperties(filtered);
  }, [filterType, sortBy]);

  const handleContact = (contact) => {
    const message = `Hi ${contact.name}, I'm interested in the property. Can you please provide more details?`;
    const whatsappUrl = `https://wa.me/${contact.phone.replace(
      /[^0-9]/g,
      ""
    )}?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, "_blank");
  };

  return (
    <div className="min-h-screen">
      {/* Filters Section */}
      <section className="filters-section">
        <div className="container">
          <div className="filters-container">
            <div className="filter-group">
              <label>🏷️ Property Type:</label>
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="filter-select"
              >
                <option value="All">All Properties</option>
                <option value="PG">🏨 PG</option>
                <option value="Flat">🏢 Flat</option>
              </select>
            </div>
            <div className="filter-group">
              <label>📊 Sort By:</label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="filter-select"
              >
                <option value="rating">⭐ Rating</option>
                <option value="price">💰 Price</option>
              </select>
            </div>
          </div>
        </div>
      </section>

      {/* Properties Grid */}
      <section className="properties-section">
        <div className="container">
          <div className="properties-grid">
            {filteredProperties.map((property, index) => (
              <div
                key={property.id}
                className="property-card"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="property-header">
                  <div className="property-image">
                    <span className="property-icon">{property.image}</span>
                    <div className="property-type-badge">{property.type}</div>
                  </div>
                  <div className="property-rating">
                    <span className="rating-stars">⭐</span>
                    <span className="rating-value">{property.rating}</span>
                    <span className="rating-reviews">({property.reviews})</span>
                  </div>
                </div>

                <div className="property-content">
                  <h3 className="property-title">{property.title}</h3>
                  <div className="property-location">
                    <span className="location-icon">📍</span>
                    {property.location}
                  </div>

                  <div className="property-price">
                    <span className="price-amount">{property.price}</span>
                    <span className="price-period">{property.period}</span>
                  </div>

                  <p className="property-description">{property.description}</p>

                  <div className="property-features">
                    <h4>🏠 Features:</h4>
                    <div className="features-grid">
                      {property.features.map((feature, idx) => (
                        <span key={idx} className="feature-tag">
                          {feature}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div className="property-amenities">
                    <h4>✨ Amenities:</h4>
                    <div className="amenities-grid">
                      {property.amenities.map((amenity, idx) => (
                        <span key={idx} className="amenity-tag">
                          {amenity}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div className="property-contact">
                    <div className="contact-info">
                      <span className="contact-name">
                        👤 {property.contact.name}
                      </span>
                      <span className="contact-phone">
                        📱 {property.contact.phone}
                      </span>
                    </div>
                    <button
                      className="contact-btn"
                      onClick={() => handleContact(property.contact)}
                    >
                      💬 WhatsApp
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      <style jsx>{`
        .filters-section {
          background: rgba(255, 255, 255, 0.95);
          padding: 2rem 0;
          backdrop-filter: blur(10px);
          border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .container {
          max-width: 1200px;
          margin: 0 auto;
          padding: 0 2rem;
        }

        .filters-container {
          display: flex;
          gap: 2rem;
          justify-content: center;
          flex-wrap: wrap;
        }

        .filter-group {
          display: flex;
          align-items: center;
          gap: 0.5rem;
        }

        .filter-group label {
          font-weight: 600;
          color: #1e293b;
        }

        .filter-select {
          padding: 0.7rem 1rem;
          border: 2px solid #e2e8f0;
          border-radius: 10px;
          background: white;
          color: #1e293b;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .filter-select:focus {
          outline: none;
          border-color: #667eea;
          box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .properties-section {
          padding: 3rem 0;
          background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
        }

        .properties-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
          gap: 2rem;
        }

        .property-card {
          background: white;
          color: #1e293b;
          border-radius: 20px;
          overflow: hidden;
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
          transition: all 0.3s ease;
          animation: slideInUp 0.8s ease-out both;
          border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .property-card:hover {
          transform: translateY(-10px);
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .property-header {
          position: relative;
          height: 200px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .property-image {
          position: relative;
        }

        .property-icon {
          font-size: 4rem;
          animation: bounce 2s infinite;
        }

        .property-type-badge {
          position: absolute;
          top: 1rem;
          left: 1rem;
          background: rgba(255, 255, 255, 0.2);
          color: white;
          padding: 0.5rem 1rem;
          border-radius: 20px;
          font-weight: 600;
          backdrop-filter: blur(10px);
        }

        .property-rating {
          position: absolute;
          top: 1rem;
          right: 1rem;
          background: rgba(255, 255, 255, 0.2);
          color: white;
          padding: 0.5rem 1rem;
          border-radius: 20px;
          backdrop-filter: blur(10px);
          display: flex;
          align-items: center;
          gap: 0.3rem;
        }

        .property-content {
          padding: 2rem;
        }

        .property-title {
          font-size: 1.4rem;
          font-weight: 700;
          margin-bottom: 0.5rem;
          color: #1e293b;
        }

        .property-location {
          color: #64748b;
          margin-bottom: 1rem;
          display: flex;
          align-items: center;
          gap: 0.5rem;
        }

        .property-price {
          font-size: 1.8rem;
          font-weight: 800;
          color: #667eea;
          margin-bottom: 1rem;
        }

        .property-description {
          color: #64748b;
          line-height: 1.6;
          margin-bottom: 1.5rem;
        }

        .property-features h4,
        .property-amenities h4 {
          font-size: 1rem;
          font-weight: 600;
          margin-bottom: 0.8rem;
          color: #1e293b;
        }

        .features-grid,
        .amenities-grid {
          display: flex;
          flex-wrap: wrap;
          gap: 0.5rem;
          margin-bottom: 1.5rem;
        }

        .feature-tag,
        .amenity-tag {
          background: #f1f5f9;
          padding: 0.4rem 0.8rem;
          border-radius: 15px;
          font-size: 0.85rem;
          color: #475569;
          border: 1px solid #e2e8f0;
        }

        .property-contact {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-top: 1rem;
          border-top: 1px solid #e2e8f0;
        }

        .contact-info {
          display: flex;
          flex-direction: column;
          gap: 0.3rem;
        }

        .contact-name,
        .contact-phone {
          font-size: 0.9rem;
          color: #64748b;
        }

        .contact-btn {
          background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
          color: white;
          border: none;
          padding: 0.8rem 1.5rem;
          border-radius: 25px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          gap: 0.5rem;
        }

        .contact-btn:hover {
          transform: scale(1.05);
          box-shadow: 0 5px 15px rgba(37, 211, 102, 0.3);
        }

        @keyframes slideInUp {
          from {
            opacity: 0;
            transform: translateY(50px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        /* Tablet Styles */
        @media (max-width: 1024px) {
          .properties-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 1.5rem;
          }

          .container {
            padding: 0 1.5rem;
          }

          .filters-container {
            gap: 1.5rem;
          }

          .filter-select {
            padding: 0.6rem 0.8rem;
            font-size: 0.9rem;
          }
        }

        /* Mobile Styles */
        @media (max-width: 768px) {
          .section-title {
            font-size: 2rem;
            text-align: center;
            margin-bottom: 1.5rem;
          }

          .filters-container {
            flex-direction: column;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
          }

          .filter-group {
            width: 100%;
            max-width: 300px;
            flex-direction: column;
            gap: 0.5rem;
            text-align: center;
          }

          .filter-group label {
            font-size: 1rem;
            margin-bottom: 0.3rem;
          }

          .filter-select {
            width: 100%;
            padding: 0.8rem 1rem;
            font-size: 1rem;
            border-radius: 12px;
            text-align: center;
          }

          .properties-grid {
            grid-template-columns: 1fr;
            gap: 1.5rem;
          }

          .property-card {
            margin: 0 0.5rem;
            border-radius: 20px;
          }

          .property-contact {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;
          }

          .contact-btn {
            justify-content: center;
            padding: 1rem;
            font-size: 1rem;
            border-radius: 12px;
          }

          .container {
            padding: 0 1rem;
          }

          .filters-section {
            padding: 1.5rem 0;
          }

          .properties-section {
            padding: 2rem 0;
          }
        }

        /* Small Mobile Styles */
        @media (max-width: 480px) {
          .section-title {
            font-size: 1.8rem;
            padding: 0 1rem;
          }

          .filters-container {
            padding: 0.5rem;
          }

          .filter-group {
            max-width: 280px;
          }

          .filter-select {
            padding: 0.9rem 1rem;
            font-size: 1.1rem;
            border-radius: 15px;
          }

          .property-card {
            margin: 0 0.25rem;
            border-radius: 18px;
          }

          .property-header {
            height: 140px;
            border-radius: 18px 18px 0 0;
          }

          .property-icon {
            font-size: 2.8rem;
          }

          .property-content {
            padding: 1.2rem;
          }

          .property-title {
            font-size: 1.1rem;
            line-height: 1.3;
            margin-bottom: 0.5rem;
          }

          .property-price {
            font-size: 1.4rem;
            margin-bottom: 0.8rem;
          }

          .property-description {
            font-size: 0.9rem;
            line-height: 1.4;
            margin-bottom: 1rem;
          }

          .features-grid,
          .amenities-grid {
            gap: 0.4rem;
            margin-bottom: 1rem;
          }

          .feature-tag,
          .amenity-tag {
            font-size: 0.75rem;
            padding: 0.4rem 0.7rem;
            border-radius: 12px;
          }

          .property-rating {
            padding: 0.4rem 0.8rem;
            font-size: 0.9rem;
            border-radius: 15px;
          }

          .contact-btn {
            padding: 1.1rem;
            font-size: 1.1rem;
            border-radius: 15px;
            font-weight: 600;
          }

          .container {
            padding: 0 0.75rem;
          }

          .filters-section {
            padding: 1rem 0;
          }

          .properties-section {
            padding: 1.5rem 0;
          }
        }

        /* Extra Small Mobile Styles */
        @media (max-width: 360px) {
          .section-title {
            font-size: 1.6rem;
          }

          .filter-group {
            max-width: 260px;
          }

          .filter-select {
            padding: 0.8rem;
            font-size: 1rem;
          }

          .property-content {
            padding: 1rem;
          }

          .property-title {
            font-size: 1rem;
          }

          .property-price {
            font-size: 1.3rem;
          }

          .feature-tag,
          .amenity-tag {
            font-size: 0.7rem;
            padding: 0.3rem 0.6rem;
          }

          .contact-btn {
            padding: 1rem;
            font-size: 1rem;
          }

          .container {
            padding: 0 0.5rem;
          }
        }
      `}</style>
    </div>
  );
}
