"use client";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import React from "react";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export default function RootLayout({ children }) {
  // Support team members who helped find flats
  const supportTeam = [
    // {
    //   id: 1,
    //   name: "<PERSON><PERSON> <PERSON>",
    //   role: "Student Helper 🎓",
    //   location: "BCAS-DU",
    //   image: "/mypic.png",
    //   description: "Helping students find perfect PGs 🏠",
    //   specialty: "Computer Science Department",
    //   experience: "IIIrd Year",
    //   rating: 4.9,
    //   properties: "10+",
    // },
    {
      id: 2,
      name: "<PERSON><PERSON>",
      role: "Fresher Guide 🌟",
      location: "BCAS-DU",
      image: "/richa.jpg",
      description:
        "WELCOME FRESHERS! New to college and feeling lost? Don't worry — I've got you covered from day one to your first fest! 🎉",
      specialty: "Botany Department",
      experience: "IIIrd Year",
      rating: 4.8,
      properties: "10+",
    },
    {
      id: 3,
      name: "Anjaneya <PERSON>",
      role: "Team Leader",
      location: "BCAS-DU",
      image: "/anjanaye.jpg",
      description:
        "Leading the team to help students find their perfect homes with dedication and expertise 🏠✨",
      specialty: "Property Expert",
      experience: "5+ Years",
      rating: 4.9,
      properties: "50+",
    },
  ];

  return (
    <html lang="en">
      <head>
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
        />
        <meta name="theme-color" content="#667eea" />
        <title>PG and Flat Details - Find Your Perfect Home</title>
        <meta
          name="description"
          content="Discover amazing PGs and flats with premium amenities and modern features. Find your perfect home with our expert team."
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
        style={{
          background: "linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%)",
          minHeight: "100vh",
          color: "#1e293b",
        }}
      >
        {/* Header */}
        <header
          style={{
            background: "#667eea",
            padding: "1rem 0",
            position: "fixed",
            width: "100%",
            top: 0,
            zIndex: 1000,
            boxShadow: "0 2px 10px rgba(0,0,0,0.1)",
          }}
        >
          <div
            style={{
              maxWidth: "1200px",
              margin: "0 auto",
              padding: "0 1rem",
              textAlign: "center",
              color: "white",
            }}
          >
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                gap: "10px",
                fontSize: "1.5rem",
                fontWeight: "700",
                marginBottom: "0.5rem",
              }}
            >
              <span style={{ fontSize: "2rem" }}>🏠</span>
              <span>PG and Flat Details</span>
            </div>
            <p
              style={{
                fontSize: "0.9rem",
                opacity: "0.9",
                margin: 0,
                fontWeight: "400",
              }}
            >
              Discover amazing PGs and flats with premium amenities and modern
              features
            </p>
          </div>
        </header>

        {/* Main Content */}
        <main style={{ marginTop: "100px", minHeight: "calc(100vh - 200px)" }}>
          {children}
        </main>

        {/* Supporting Team Section */}
        <section
          style={{
            background: "#1e293b",
            color: "white",
            padding: "3rem 0",
          }}
        >
          <div
            style={{ maxWidth: "1200px", margin: "0 auto", padding: "0 1rem" }}
          >
            <h2
              style={{
                textAlign: "center",
                fontSize: "2rem",
                fontWeight: "700",
                marginBottom: "1rem",
                color: "#fbbf24",
              }}
            >
              🏠 Our Supporting Team 🔍
            </h2>
            <p
              style={{
                textAlign: "center",
                fontSize: "1rem",
                opacity: "0.9",
                marginBottom: "2rem",
              }}
            >
              The amazing people who helped hundreds find their perfect homes
            </p>

            <div
              style={{
                display: "grid",
                gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))",
                gap: "1.5rem",
                margin: "0 auto",
              }}
            >
              {supportTeam.map((member) => (
                <div
                  key={member.id}
                  style={{
                    background: "#667eea",
                    borderRadius: "15px",
                    padding: "1.5rem",
                    textAlign: "center",
                    boxShadow: "0 4px 15px rgba(0,0,0,0.1)",
                  }}
                >
                  <div
                    style={{
                      width: "80px",
                      height: "80px",
                      borderRadius: "50%",
                      overflow: "hidden",
                      margin: "0 auto 1rem auto",
                      border: "2px solid white",
                    }}
                  >
                    <img
                      src={member.image}
                      alt={member.name}
                      style={{
                        width: "100%",
                        height: "100%",
                        objectFit: "cover",
                      }}
                    />
                  </div>
                  <h3
                    style={{
                      fontSize: "1.1rem",
                      fontWeight: "700",
                      marginBottom: "0.5rem",
                      color: "#fbbf24",
                    }}
                  >
                    {member.name}
                  </h3>
                  <p
                    style={{
                      fontSize: "0.9rem",
                      marginBottom: "0.5rem",
                      color: "white",
                    }}
                  >
                    {member.role}
                  </p>
                  <p
                    style={{
                      fontSize: "0.8rem",
                      opacity: "0.8",
                      color: "white",
                    }}
                  >
                    {member.description}
                  </p>
                </div>
              ))}
            </div>

            {/* Contact CTA */}
            <div
              style={{
                textAlign: "center",
                marginTop: "3rem",
                padding: "2rem",
                background: "rgba(255,255,255,0.1)",
                borderRadius: "15px",
              }}
            >
              <h3
                style={{
                  fontSize: "1.5rem",
                  fontWeight: "700",
                  marginBottom: "1rem",
                }}
              >
                Need Help Finding Your Perfect Home? 🏡
              </h3>
              <p
                style={{
                  fontSize: "1.1rem",
                  marginBottom: "2rem",
                  opacity: "0.9",
                }}
              >
                Our expert team is here to help you 24/7
              </p>
              <a
                href="tel:+919305773385"
                style={{
                  background: "#10b981",
                  color: "white",
                  padding: "1rem 2rem",
                  borderRadius: "10px",
                  textDecoration: "none",
                  fontWeight: "600",
                  display: "inline-block",
                }}
              >
                📞 Call Now: +91 93057 73385
              </a>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer
          style={{
            background: "#0f172a",
            color: "white",
            padding: "2rem 0 1rem 0",
            textAlign: "center",
          }}
        >
          <div
            style={{ maxWidth: "1200px", margin: "0 auto", padding: "0 1rem" }}
          >
            <p style={{ margin: 0, opacity: "0.8" }}>
              © 2024 PG and Flat Details. Made with ❤️ for students.
            </p>
          </div>
        </footer>

        {/* Mobile Responsive Styles */}
        <style jsx>{`
          @media (max-width: 768px) {
            header {
              padding: 0.8rem 0 !important;
            }

            header div {
              padding: 0 0.5rem !important;
            }

            header div div {
              font-size: 1.3rem !important;
              flex-direction: column !important;
              gap: 5px !important;
            }

            header div div span:first-child {
              font-size: 1.8rem !important;
            }

            header p {
              font-size: 0.8rem !important;
              padding: 0 1rem !important;
            }

            main {
              margin-top: 110px !important;
            }

            section div {
              padding: 0 0.5rem !important;
            }

            section h2 {
              font-size: 1.5rem !important;
            }

            section div div {
              grid-template-columns: 1fr !important;
              gap: 1rem !important;
            }
          }

          @media (max-width: 480px) {
            header div div {
              font-size: 1.1rem !important;
            }

            header div div span:first-child {
              font-size: 1.5rem !important;
            }

            header p {
              font-size: 0.75rem !important;
            }

            main {
              margin-top: 100px !important;
            }

            section h2 {
              font-size: 1.3rem !important;
            }
          }
        `}</style>
      </body>
    </html>
  );
}
