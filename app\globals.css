@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), Arial, Helvetica, sans-serif;
  scroll-behavior: smooth;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

* {
  box-sizing: border-box;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

/* Selection styles */
::selection {
  background: rgba(102, 126, 234, 0.3);
  color: #1e293b;
}

/* Focus styles */
button:focus,
a:focus,
input:focus,
select:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* Smooth transitions for all interactive elements */
button,
a,
input,
select {
  transition: all 0.3s ease;
}

/* Loading animation */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Mobile Touch Optimizations */
@media (max-width: 768px) {
  html {
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
  }

  body {
    -webkit-overflow-scrolling: touch;
    touch-action: manipulation;
  }

  button, select, input {
    touch-action: manipulation;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
  }

  /* Ensure minimum touch target size */
  button, select, a {
    min-height: 44px;
    min-width: 44px;
  }
}
